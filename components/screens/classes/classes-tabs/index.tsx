import React from "react";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";

interface ClassesTabsProps {
  selectedTab: "classes" | "appointment";
  onTabSelect: (tab: "classes" | "appointment") => void;
}

export const ClassesTabs: React.FC<ClassesTabsProps> = ({
  selectedTab,
  onTabSelect,
}) => {
  return (
    <HStack
      className="mx-4 bg-[#E6F9FC] rounded-xl  border border-background-200"
      space="xs"
    >
      <Pressable
        onPress={() => onTabSelect("classes")}
        className={`flex-1 py-3 px-4 rounded-lg items-center ${
          selectedTab === "classes" ? "bg-primary-500" : "bg-transparent"
        }`}
      >
        <Text
          className={`text-sm font-dm-sans-medium ${
            selectedTab === "classes" ? "text-base" : "text-typography-600"
          }`}
        >
          Classes
        </Text>
      </Pressable>

      <Pressable
        onPress={() => onTabSelect("appointment")}
        className={`flex-1 py-3 px-4 rounded-lg items-center ${
          selectedTab === "appointment" ? "bg-primary-500" : "bg-transparent"
        }`}
      >
        <Text
          className={`font-dm-sans-medium ${
            selectedTab === "appointment"
              ? "text-background-light"
              : "text-typography-600"
          }`}
        >
          Appointment
        </Text>
      </Pressable>
    </HStack>
  );
};

export default ClassesTabs;
