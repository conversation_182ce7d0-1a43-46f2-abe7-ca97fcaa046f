import React, { useState } from "react";
import { <PERSON><PERSON>View } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Pressable } from "@/components/ui/pressable";
import { Box } from "@/components/ui/box";
import { Badge, BadgeText } from "@/components/ui/badge";
import { Icon } from "@/components/ui/icon";
import {
  Heart,
  Clock,
  MapPin,
  Users,
  Calendar,
  Share,
  Star,
  Dumbbell,
  CheckCircle,
  AlertCircle,
} from "lucide-react-native";
import { ClassItem } from "../../../../data/screens/classes";
import { Image } from "react-native";

interface ClassDetailsProps {
  classItem: ClassItem;
  isLoading?: boolean;
}

const ClassDetails: React.FC<ClassDetailsProps> = ({
  classItem,
  isLoading = false,
}) => {
  const [isFavorite, setIsFavorite] = useState(classItem.isFavorite);

  if (isLoading) {
    return (
      <VStack className="flex-1 justify-center items-center px-4">
        <Text className="text-lg font-dm-sans-medium text-typography-600">
          Loading class details...
        </Text>
      </VStack>
    );
  }

  const getStatusButton = () => {
    switch (classItem.status) {
      case "available":
        return (
          <Button className="bg-primary-500 border-0 flex-1">
            <ButtonText className="text-white font-dm-sans-medium">
              {classItem.type === "Virtual"
                ? "Reserve virtually"
                : "Reserve live"}
            </ButtonText>
          </Button>
        );
      case "cancelled":
        return (
          <Button
            variant="outline"
            className="border-error-300 bg-error-50 flex-1"
            disabled
          >
            <ButtonText className="text-error-600 font-dm-sans-medium">
              Class Cancelled
            </ButtonText>
          </Button>
        );
      case "waitlist":
        return (
          <Button
            variant="outline"
            className="border-success-300 bg-success-50 flex-1"
          >
            <ButtonText className="text-success-600 font-dm-sans-medium">
              Join Waitlist
            </ButtonText>
          </Button>
        );
      case "reserved":
        return (
          <Button className="bg-primary-500 border-0 flex-1">
            <ButtonText className="text-white font-dm-sans-medium">
              View Reservation
            </ButtonText>
          </Button>
        );
      default:
        return null;
    }
  };

  const getIntensityColor = () => {
    switch (classItem.intensity) {
      case "Low":
        return "text-success-600";
      case "Medium":
        return "text-warning-600";
      case "High":
        return "text-error-600";
      default:
        return "text-typography-600";
    }
  };

  const getIntensityBgColor = () => {
    switch (classItem.intensity) {
      case "Low":
        return "bg-success-100";
      case "Medium":
        return "bg-warning-100";
      case "High":
        return "bg-error-100";
      default:
        return "bg-background-100";
    }
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      <VStack space="lg" className="pb-6">
        {/* Hero Image */}
        <Box className="h-64 bg-background-100 relative">
          <Image
            source={{
              uri: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=256&fit=crop",
            }}
            className="w-full h-full"
            resizeMode="cover"
          />

          {/* Overlay with type badge */}
          <Box className="absolute top-4 left-4">
            <Box
              className={`px-3 py-1 rounded-full ${
                classItem.type === "Paid" ? "bg-primary-500" : "bg-success-500"
              }`}
            >
              <Text className="text-white font-dm-sans-medium text-sm">
                {classItem.type}
              </Text>
            </Box>
          </Box>

          {/* Share and favorite buttons */}
          <HStack className="absolute top-4 right-4" space="sm">
            <Pressable className="p-2 bg-white/90 rounded-full">
              <Icon as={Share} size="sm" className="text-typography-900" />
            </Pressable>
            <Pressable
              onPress={() => setIsFavorite(!isFavorite)}
              className="p-2 bg-white/90 rounded-full"
            >
              <Icon
                as={Heart}
                size="sm"
                className={
                  isFavorite
                    ? "text-error-500 fill-error-500"
                    : "text-typography-600"
                }
              />
            </Pressable>
          </HStack>
        </Box>

        {/* Main Content */}
        <VStack space="lg" className="px-4">
          {/* Title and Basic Info */}
          <VStack space="md">
            <VStack space="sm">
              <Text className="text-2xl font-dm-sans-bold text-typography-900">
                {classItem.title}
              </Text>

              <HStack className="items-center" space="md">
                <HStack className="items-center" space="xs">
                  <Icon
                    as={Calendar}
                    size="sm"
                    className="text-typography-600"
                  />
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    {classItem.date}
                  </Text>
                </HStack>

                <HStack className="items-center" space="xs">
                  <Icon as={Clock} size="sm" className="text-typography-600" />
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    {classItem.time}
                  </Text>
                </HStack>
              </HStack>

              <HStack className="items-center" space="md">
                <HStack className="items-center" space="xs">
                  <Icon as={MapPin} size="sm" className="text-typography-600" />
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    {classItem.location}
                  </Text>
                </HStack>

                {classItem.room && (
                  <Text className="text-sm font-dm-sans-regular text-typography-500">
                    • {classItem.room}
                  </Text>
                )}
              </HStack>
            </VStack>

            {/* Intensity and Slots */}
            <HStack className="items-center justify-between">
              <HStack className="items-center" space="md">
                <Box
                  className={`px-3 py-1 rounded-lg ${getIntensityBgColor()}`}
                >
                  <Text
                    className={`text-sm font-dm-sans-medium ${getIntensityColor()}`}
                  >
                    {classItem.intensity} Intensity
                  </Text>
                </Box>

                <HStack className="items-center" space="xs">
                  <Icon as={Users} size="sm" className="text-typography-600" />
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    {classItem.slotsLeft} of {classItem.totalSlots} spots left
                  </Text>
                </HStack>
              </HStack>
            </HStack>
          </VStack>

          {/* Description */}
          <VStack space="sm">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Description
            </Text>
            <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
              {classItem.description}
            </Text>
          </VStack>

          {/* Instructor Section */}
          <VStack space="sm">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Instructor
            </Text>
            <HStack space="sm" className="items-center">
              <Box className="w-12 h-12 bg-background-100 rounded-full items-center justify-center">
                {classItem.instructorImage ? (
                  <Image
                    source={{ uri: classItem.instructorImage }}
                    className="w-12 h-12 rounded-full"
                    resizeMode="cover"
                  />
                ) : (
                  <Text className="text-sm font-dm-sans-bold text-typography-900">
                    {classItem.avatar}
                  </Text>
                )}
              </Box>
              <VStack className="flex-1" space="xs">
                <HStack className="items-center" space="xs">
                  <Text className="text-base font-dm-sans-bold text-typography-900">
                    {classItem.instructor}
                  </Text>
                  <Badge action="info" variant="outline" size="sm">
                    <BadgeText>Substitute</BadgeText>
                  </Badge>
                </HStack>
                <HStack className="items-center" space="xs">
                  <Icon
                    as={Star}
                    size="xs"
                    className="text-warning-500 fill-warning-500"
                  />
                  <Text className="text-sm font-dm-sans-medium text-typography-600">
                    4.8 (124 reviews)
                  </Text>
                  <Pressable>
                    <Text className="text-sm font-dm-sans-medium text-primary-500">
                      Read about
                    </Text>
                  </Pressable>
                </HStack>
              </VStack>
            </HStack>
            <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
              {classItem.instructorBio}
            </Text>
          </VStack>

          {/* Equipment Section */}
          {classItem.equipment && classItem.equipment.length > 0 && (
            <VStack space="sm">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Equipment Needed
              </Text>
              <VStack space="xs">
                {classItem.equipment.map((item, index) => (
                  <HStack key={index} className="items-center" space="xs">
                    <Icon
                      as={Dumbbell}
                      size="xs"
                      className="text-primary-500"
                    />
                    <Text className="text-sm font-dm-sans-regular text-typography-600">
                      {item}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            </VStack>
          )}

          {/* Prerequisites Section */}
          {classItem.prerequisites && classItem.prerequisites.length > 0 && (
            <VStack space="sm">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Prerequisites
              </Text>
              <VStack space="xs">
                {classItem.prerequisites.map((item, index) => (
                  <HStack key={index} className="items-center" space="xs">
                    <Icon
                      as={CheckCircle}
                      size="xs"
                      className="text-success-500"
                    />
                    <Text className="text-sm font-dm-sans-regular text-typography-600">
                      {item}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            </VStack>
          )}

          {/* Tags Section */}
          {classItem.tags && classItem.tags.length > 0 && (
            <VStack space="sm">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Class Tags
              </Text>
              <HStack className="flex-wrap" space="xs">
                {classItem.tags.map((tag, index) => (
                  <Box
                    key={index}
                    className="bg-primary-100 px-3 py-1 rounded-full mb-2 mr-2"
                  >
                    <Text className="text-sm font-dm-sans-medium text-primary-600">
                      {tag}
                    </Text>
                  </Box>
                ))}
              </HStack>
            </VStack>
          )}

          {/* Price Section */}
          {classItem.price && (
            <VStack space="sm">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Price
              </Text>
              <Text className="text-2xl font-dm-sans-bold text-primary-500">
                ${classItem.price}
              </Text>
            </VStack>
          )}

          {/* Status Warning for Cancelled Classes */}
          {classItem.status === "cancelled" && (
            <HStack
              className="bg-error-50 border border-error-200 rounded-lg p-4"
              space="sm"
            >
              <Icon
                as={AlertCircle}
                size="sm"
                className="text-error-500 mt-0.5"
              />
              <VStack className="flex-1" space="xs">
                <Text className="text-sm font-dm-sans-bold text-error-700">
                  Class Cancelled
                </Text>
                <Text className="text-sm font-dm-sans-regular text-error-600">
                  This class has been cancelled due to low enrollment. You will
                  be automatically refunded.
                </Text>
              </VStack>
            </HStack>
          )}
        </VStack>

        {/* Fixed Bottom Action Bar */}
        <Box className="px-4 py-4 bg-white border-t border-background-200">
          <HStack space="sm">{getStatusButton()}</HStack>
        </Box>
      </VStack>
    </ScrollView>
  );
};

export default ClassDetails;
