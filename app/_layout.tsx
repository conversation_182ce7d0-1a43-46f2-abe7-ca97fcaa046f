import "@/global.css";
import { useContext } from "react";
import { Stack } from "expo-router";
import { useFonts } from "expo-font";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { StatusBar } from "expo-status-bar";
import { ThemeContext, ThemeProvider } from "@/contexts/theme-context";
import {
  DMSans_400Regular,
  DMSans_500Medium,
  DMSans_700Bold,
} from "@expo-google-fonts/dm-sans";

import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { AuthContextProvider } from "@/modules/login/auth-provider";
import { QueryClient } from "@tanstack/react-query";
import { clientPersister } from "@/lib/query-cache";
import { KeyboardProvider } from "react-native-keyboard-controller";

const MainLayout = () => {
  const { colorMode }: any = useContext(ThemeContext);
  const [fontsLoaded] = useFonts({
    "dm-sans-regular": DMSans_400Regular,
    "dm-sans-medium": DMSans_500Medium,
    "dm-sans-bold": DMSans_700Bold,
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <GluestackUIProvider mode={colorMode}>
      <StatusBar translucent />
      <Stack>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen
          name="terms"
          options={{
            presentation: "formSheet",
            animation: "slide_from_bottom",
            gestureDirection: "vertical",
            sheetGrabberVisible: true,
            sheetInitialDetentIndex: 0,
            sheetAllowedDetents: [0, 0.5, 1],
            title: "Terms of Use & Privacy Policy",
          }}
        />
      </Stack>
    </GluestackUIProvider>
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 1000 * 60 * 60 * 1, // 1 hour
      staleTime: 2000,
      refetchInterval: 30 * 1000,
      refetchIntervalInBackground: true,
      networkMode: "offlineFirst",
      refetchOnWindowFocus: true,
    },
  },
});

export default function RootLayout() {
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister: clientPersister,
        maxAge: Infinity,
      }}
    >
      <ThemeProvider>
        <KeyboardProvider>
          <AuthContextProvider>
            <MainLayout />
          </AuthContextProvider>
        </KeyboardProvider>
      </ThemeProvider>
    </PersistQueryClientProvider>
  );
}
