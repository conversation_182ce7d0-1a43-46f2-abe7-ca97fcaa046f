import React from "react";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

import { Button } from "@/components/ui/button";
import { useSession } from "@/modules/login/auth-provider";
import BiometricSettings from "@/components/screens/settings/biometric-settings";

const Settings = () => {
  const { signOut } = useSession();

  return (
    <VStack space="md" className="bg-background-0 mt-12">
      {/* Biometric Settings */}
      <BiometricSettings />

      <VStack className="px-4" space="md">
        <Text className="font-semibold">Logout</Text>
        <VStack className="px-4" space="md">
          <Button
            onPress={signOut}
            variant="solid"
            className="w-full bg-[#00697B] rounded-full"
          >
            <Text className="text-white font-dm-sans-medium text-center">
              Logout
            </Text>
          </Button>
        </VStack>
        {/* <HStack space="sm">
          <ThemeCard
            title="Light Mode"
            icon={SunIcon}
            onPress={() => setColorMode("light")}
            active={colorMode === "light"}
          />
          <ThemeCard
            title="Dark Mode"
            icon={MoonIcon}
            onPress={() => setColorMode("dark")}
            active={colorMode === "dark"}
          />
        </HStack> */}
      </VStack>
    </VStack>
  );
};

export default Settings;
