import { router } from "expo-router";
import ky from "ky";
// import { showErrorToast } from "~/components/toast";

import { setStorageItemAsync } from "@/modules/login/hooks/useStorageState";

import { BASE_API_URL_CLIENT } from "@/constants/base-url";
import {
  getSession,
  signOut,
  UPACE_TOKEN,
} from "@/modules/login/auth-provider";

export const api = ky.create({
  prefixUrl: BASE_API_URL_CLIENT,
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        const session = await getSession();
        request.headers.set("Authorization", `Bearer ${session?.token}`);
      },
    ],
    afterResponse: [
      async (request, _options, response) => {
        const resp = (await response.json()) as {
          code: number;
          reason: string;
        };

        if (resp?.code === 401 && resp?.reason === "Invalid Token") {
          const res = await refreshToken();
          request.headers.set("Authorization", `Bearer ${res?.token}`);
          return ky(request);
        }
      },
    ],
    beforeError: [
      async (error) => {
        const errorResponse = (await error.response.json()) as {
          message: string;
        };

        // showErrorToast(errorResponse?.message);
        return errorResponse?.message as never;
      },
    ],
  },
});

const refreshToken = async () => {
  try {
    const session = await getSession();

    const resp = await api
      .post<{ message: string; success: boolean; token: string }>(
        "auth/token/refresh",
        {
          json: {
            refresh_token: session?.refresh_token,
          },
        }
      )
      .json();

    setStorageItemAsync(UPACE_TOKEN, {
      ...session,
      token: resp?.token,
    });
    return resp;
  } catch (error: unknown) {
    // showErrorToast("Session expired, you have been logout");
    await signOut();
    router.replace("/(auth)/sign-in");
  }
};
